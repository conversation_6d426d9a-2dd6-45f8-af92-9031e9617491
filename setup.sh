#!/bin/bash
# setup.sh - Complete setup script for Karakeep on GKE

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="jamcrank-project"  # Update this
CLUSTER_NAME="karakeep-cluster"
ZONE="us-central1-a"

# Function to print colored output
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    command -v terraform >/dev/null 2>&1 || error "Terraform is not installed"
    command -v ansible >/dev/null 2>&1 || error "Ansible is not installed"
    command -v kubectl >/dev/null 2>&1 || error "kubectl is not installed"
    command -v gcloud >/dev/null 2>&1 || error "gcloud CLI is not installed"
    
    # Check if logged into gcloud
    gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q . || error "Please login to gcloud: gcloud auth login"
    
    log "Prerequisites check passed!"
}

# Check environment variables
check_env_vars() {
    log "Checking environment variables..."
    
    if [[ -z "${GEMINI_API_KEY:-}" ]]; then
        error "GEMINI_API_KEY environment variable is not set"
    fi
    
    if [[ -z "${TAILSCALE_AUTH_KEY:-}" ]]; then
        warn "TAILSCALE_AUTH_KEY environment variable is not set. Tailscale deployment will fail."
        read -p "Continue anyway? (y/N): " continue_without_tailscale
        if [[ ! $continue_without_tailscale =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    log "Environment variables check passed!"
}

# Set up Terraform
setup_terraform() {
    log "Setting up Terraform..."
    
    cd terraform
    
    # Initialize Terraform
    terraform init
    
    # Create terraform.tfvars if it doesn't exist
    if [[ ! -f terraform.tfvars ]]; then
        cat > terraform.tfvars <<EOF
project_id = "$PROJECT_ID"
region     = "us-central1"
zone       = "$ZONE"
EOF
        log "Created terraform.tfvars file"
    fi
    
    # Plan and apply
    terraform plan
    read -p "Apply Terraform configuration? (y/N): " apply_terraform
    if [[ $apply_terraform =~ ^[Yy]$ ]]; then
        terraform apply -auto-approve
        log "Terraform applied successfully!"
    else
        warn "Terraform not applied. You'll need to run 'terraform apply' manually."
    fi
    
    cd ..
}

# Set up Ansible
setup_ansible() {
    log "Setting up Ansible..."
    
    cd ansible
    
    # Install Ansible collections
    ansible-galaxy collection install -r requirements.yml
    
    # Update project ID in playbook if needed
    sed -i.bak "s/jamcrank-project/$PROJECT_ID/g" karakeep-deploy.yml
    
    log "Ansible setup complete!"
    cd ..
}

# Deploy applications
deploy_apps() {
    log "Deploying applications with Ansible..."
    
    cd ansible
    
    # Run the playbook
    ansible-playbook karakeep-deploy.yml
    
    log "Applications deployed successfully!"
    cd ..
}

# Display access information
show_access_info() {
    log "Deployment complete! Here's how to access your Karakeep app:"
    echo ""
    echo "1. Port forward to access locally:"
    echo "   kubectl port-forward -n karakeep svc/karakeep-service 8080:80"
    echo "   Then visit: http://localhost:8080"
    echo ""
    echo "2. Check deployment status:"
    echo "   kubectl get pods -n karakeep"
    echo ""
    echo "3. View logs:"
    echo "   kubectl logs -n karakeep deployment/karakeep"
    echo ""
    echo "4. Tailscale access:"
    echo "   - Approve the subnet router in your Tailscale admin panel"
    echo "   - Access the cluster via Tailscale network"
    echo ""
    echo "Cost optimization tips:"
    echo "- This setup uses spot instances (60-91% cheaper)"
    echo "- Single node, minimal resources"
    echo "- No external load balancer"
    echo "- Estimated cost: ~$15-25/month"
}

# Cleanup function
cleanup() {
    log "To destroy the infrastructure later, run:"
    echo "cd terraform && terraform destroy"
}

# Main execution
main() {
    log "Starting Karakeep GKE setup..."
    
    check_prerequisites
    check_env_vars
    setup_terraform
    setup_ansible
    deploy_apps
    show_access_info
    
    log "Setup completed successfully! 🎉"
}

# Run main function
main "$@"
