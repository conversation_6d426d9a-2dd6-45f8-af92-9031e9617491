# Makefile for Karakeep GKE deployment
# Usage: make <target>

.PHONY: help setup deploy destroy status logs port-forward check-env

# Default target
help: ## Show this help message
	@echo "Karakeep GKE Deployment Commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Configuration
PROJECT_ID ?= jamcrank-project
CLUSTER_NAME = karakeep-cluster
ZONE = us-central1-a
NAMESPACE = karakeep

check-env: ## Check if required environment variables are set
	@echo "Checking environment variables..."
	@test -n "$(GEMINI_API_KEY)" || (echo "ERROR: GEMINI_API_KEY not set" && exit 1)
	@echo "✓ GEMINI_API_KEY is set"
	@if [ -z "$(TAILSCALE_AUTH_KEY)" ]; then echo "⚠ TAILSCALE_AUTH_KEY not set (optional)"; else echo "✓ TAILSCALE_AUTH_KEY is set"; fi

setup: check-env ## Run complete setup (Terraform + Ansible)
	@echo "Starting complete setup..."
	./setup.sh

terraform-init: ## Initialize Terraform
	cd terraform && terraform init

terraform-plan: ## Plan Terraform deployment
	cd terraform && terraform plan

terraform-apply: ## Apply Terraform configuration
	cd terraform && terraform apply

terraform-destroy: ## Destroy Terraform infrastructure
	cd terraform && terraform destroy

ansible-deps: ## Install Ansible dependencies
	cd ansible && ansible-galaxy collection install -r requirements.yml

deploy: check-env ## Deploy applications with Ansible
	cd ansible && ansible-playbook karakeep-deploy.yml

status: ## Check deployment status
	@echo "Cluster info:"
	kubectl cluster-info
	@echo ""
	@echo "Namespace status:"
	kubectl get all -n $(NAMESPACE)
	@echo ""
	@echo "Pod status:"
	kubectl get pods -n $(NAMESPACE) -o wide

logs: ## Show application logs
	@echo "Karakeep logs:"
	kubectl logs -n $(NAMESPACE) deployment/karakeep --tail=50
	@echo ""
	@echo "Tailscale logs:"
	kubectl logs -n $(NAMESPACE) deployment/tailscale-subnet-router --tail=20

port-forward: ## Start port forwarding to access the app
	@echo "Starting port forward on http://localhost:8080"
	@echo "Press Ctrl+C to stop"
	kubectl port-forward -n $(NAMESPACE) svc/karakeep-service 8080:80

scale-up: ## Scale Karakeep to 2 replicas
	kubectl scale -n $(NAMESPACE) deployment/karakeep --replicas=2

scale-down: ## Scale Karakeep to 1 replica
	kubectl scale -n $(NAMESPACE) deployment/karakeep --replicas=1

restart: ## Restart Karakeep deployment
	kubectl rollout restart -n $(NAMESPACE) deployment/karakeep

describe-karakeep: ## Describe Karakeep deployment
	kubectl describe -n $(NAMESPACE) deployment/karakeep

describe-tailscale: ## Describe Tailscale deployment
	kubectl describe -n $(NAMESPACE) deployment/tailscale-subnet-router

get-credentials: ## Get GKE cluster credentials
	gcloud container clusters get-credentials $(CLUSTER_NAME) --zone $(ZONE) --project $(PROJECT_ID)

costs: ## Show estimated monthly costs
	@echo "Estimated monthly costs for this setup:"
	@echo "• e2-micro spot instance: ~$$4-8"
	@echo "• Persistent disk (10GB): ~$$0.40"
	@echo "• GKE cluster management: Free (under 5 nodes)"
	@echo "• Networking: ~$$5-10"
	@echo "• Total estimated: $$15-25/month"
	@echo ""
	@echo "Actual costs may vary based on usage."

clean-failed-pods: ## Clean up failed/completed pods
	kubectl delete pods -n $(NAMESPACE) --field-selector=status.phase=Failed
	kubectl delete pods -n $(NAMESPACE) --field-selector=status.phase=Succeeded

update-karakeep: ## Update Karakeep to latest image
	kubectl set image -n $(NAMESPACE) deployment/karakeep karakeep=karakeep:latest
	kubectl rollout status -n $(NAMESPACE) deployment/karakeep

shell-karakeep: ## Get shell access to Karakeep pod
	kubectl exec -n $(NAMESPACE) -it deployment/karakeep -- /bin/sh

shell-tailscale: ## Get shell access to Tailscale pod
	kubectl exec -n $(NAMESPACE) -it deployment/tailscale-subnet-router -- /bin/sh

tailscale-status: ## Check Tailscale connection status
	kubectl exec -n $(NAMESPACE) deployment/tailscale-subnet-router -- tailscale status

secrets: ## Show secrets (values hidden)
	kubectl get secrets -n $(NAMESPACE)

config: ## Show current configuration
	@echo "Project ID: $(PROJECT_ID)"
	@echo "Cluster: $(CLUSTER_NAME)"
	@echo "Zone: $(ZONE)"
	@echo "Namespace: $(NAMESPACE)"
	@echo ""
	@echo "Current context:"
	kubectl config current-context

destroy: ## Destroy everything (prompt for confirmation)
	@echo "This will destroy the entire GKE cluster and all applications."
	@read -p "Are you sure? Type 'yes' to continue: " confirm && [ "$$confirm" = "yes" ] || (echo "Cancelled" && exit 1)
	cd terraform && terraform destroy

monitor: ## Monitor pods in real-time
	watch -n 2 kubectl get pods -n $(NAMESPACE) -o wide

# Development helpers
dev-setup: terraform-init ansible-deps ## Setup development environment
	@echo "Development environment ready!"

quick-deploy: check-env deploy status ## Quick deployment (assumes cluster exists)
	@echo "Quick deployment complete!"

full-setup: setup status ## Full setup with status check
	@echo "Full setup complete!"

