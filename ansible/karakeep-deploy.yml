# ansible/karakeep-deploy.yml
---
- name: Deploy Karakeep App and Tailscale to GKE
  hosts: localhost
  gather_facts: false
  vars:
    cluster_name: "karakeep-cluster"
    cluster_zone: "asia-southeast1-a"
    project_id: "karakeep-app"  # Update with actual project ID
    karakeep_image: "karakeep:latest"  # Update with actual image
    gemini_api_key: "{{ lookup('env', 'GEMINI_API_KEY') }}"
    tailscale_auth_key: "{{ lookup('env', 'TAILSCALE_AUTH_KEY') }}"
    
  tasks:
    - name: Ensure kubectl is configured for GKE cluster
      shell: |
        gcloud container clusters get-credentials {{ cluster_name }} \
          --zone {{ cluster_zone }} \
          --project {{ project_id }}
      
    - name: Create karakeep namespace
      kubernetes.core.k8s:
        name: karakeep
        api_version: v1
        kind: Namespace
        state: present
        
    - name: Create secret for Gemini API key
      kubernetes.core.k8s:
        state: present
        definition:
          apiVersion: v1
          kind: Secret
          metadata:
            name: karakeep-secrets
            namespace: karakeep
          type: Opaque
          stringData:
            GEMINI_API_KEY: "{{ gemini_api_key }}"
            
    - name: Create secret for Tailscale auth key
      kubernetes.core.k8s:
        state: present
        definition:
          apiVersion: v1
          kind: Secret
          metadata:
            name: tailscale-auth
            namespace: karakeep
          type: Opaque
          stringData:
            TS_AUTHKEY: "{{ tailscale_auth_key }}"
            
    - name: Deploy Karakeep application
      kubernetes.core.k8s:
        state: present
        definition:
          apiVersion: apps/v1
          kind: Deployment
          metadata:
            name: karakeep
            namespace: karakeep
            labels:
              app: karakeep
          spec:
            replicas: 1
            selector:
              matchLabels:
                app: karakeep
            template:
              metadata:
                labels:
                  app: karakeep
              spec:
                containers:
                - name: karakeep
                  image: "{{ karakeep_image }}"
                  ports:
                  - containerPort: 8080
                    name: http
                  env:
                  - name: GEMINI_API_KEY
                    valueFrom:
                      secretKeyRef:
                        name: karakeep-secrets
                        key: GEMINI_API_KEY
                  - name: LLM_PROVIDER
                    value: "gemini"
                  - name: GEMINI_MODEL
                    value: "gemini-2.0-flash-exp"
                  resources:
                    requests:
                      memory: "128Mi"
                      cpu: "100m"
                    limits:
                      memory: "256Mi"
                      cpu: "200m"
                      
    - name: Create Karakeep service
      kubernetes.core.k8s:
        state: present
        definition:
          apiVersion: v1
          kind: Service
          metadata:
            name: karakeep-service
            namespace: karakeep
          spec:
            selector:
              app: karakeep
            ports:
            - port: 80
              targetPort: 8080
              name: http
            type: ClusterIP
            
    - name: Deploy Tailscale subnet router
      kubernetes.core.k8s:
        state: present
        definition:
          apiVersion: apps/v1
          kind: Deployment
          metadata:
            name: tailscale-subnet-router
            namespace: karakeep
          spec:
            replicas: 1
            selector:
              matchLabels:
                app: tailscale-subnet-router
            template:
              metadata:
                labels:
                  app: tailscale-subnet-router
              spec:
                serviceAccountName: tailscale
                initContainers:
                - name: sysctler
                  image: busybox
                  securityContext:
                    privileged: true
                  command: ["/bin/sh"]
                  args:
                    - -c
                    - sysctl -w net.ipv4.ip_forward=1 net.ipv6.conf.all.forwarding=1
                  resources:
                    requests:
                      cpu: 1m
                      memory: 1Mi
                containers:
                - name: tailscale
                  imagePullPolicy: Always
                  image: tailscale/tailscale:latest
                  env:
                  - name: TS_AUTHKEY
                    valueFrom:
                      secretKeyRef:
                        name: tailscale-auth
                        key: TS_AUTHKEY
                  - name: TS_ROUTES
                    value: "10.0.0.0/8"
                  - name: TS_HOSTNAME
                    value: "karakeep-gke"
                  securityContext:
                    capabilities:
                      add:
                      - NET_ADMIN
                  resources:
                    requests:
                      cpu: 50m
                      memory: 64Mi
                    limits:
                      cpu: 100m
                      memory: 128Mi
                      
    - name: Create Tailscale service account
      kubernetes.core.k8s:
        state: present
        definition:
          apiVersion: v1
          kind: ServiceAccount
          metadata:
            name: tailscale
            namespace: karakeep
            
    - name: Create Tailscale cluster role
      kubernetes.core.k8s:
        state: present
        definition:
          apiVersion: rbac.authorization.k8s.io/v1
          kind: ClusterRole
          metadata:
            name: tailscale
          rules:
          - apiGroups: [""]
            resources: ["nodes"]
            verbs: ["list"]
            
    - name: Create Tailscale cluster role binding
      kubernetes.core.k8s:
        state: present
        definition:
          apiVersion: rbac.authorization.k8s.io/v1
          kind: ClusterRoleBinding
          metadata:
            name: tailscale
          roleRef:
            apiGroup: rbac.authorization.k8s.io
            kind: ClusterRole
            name: tailscale
          subjects:
          - kind: ServiceAccount
            name: tailscale
            namespace: karakeep
            
    - name: Wait for Karakeep deployment to be ready
      kubernetes.core.k8s_info:
        api_version: apps/v1
        kind: Deployment
        name: karakeep
        namespace: karakeep
        wait_condition:
          type: Available
          status: "True"
        wait_timeout: 300
        
    - name: Get service information
      kubernetes.core.k8s_info:
        api_version: v1
        kind: Service
        name: karakeep-service
        namespace: karakeep
      register: service_info
      
    - name: Display access information
      debug:
        msg: |
          Karakeep has been deployed successfully!
          
          To access the application:
          1. Port forward: kubectl port-forward -n karakeep svc/karakeep-service 8080:80
          2. Access at: http://localhost:8080
          
          Or access via Tailscale once the subnet router is approved in your Tailscale admin panel.

