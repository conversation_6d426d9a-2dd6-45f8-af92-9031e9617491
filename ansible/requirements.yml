# ansible/requirements.yml
---
collections:
  - name: kubernetes.core
    version: ">=2.3.0"
  - name: google.cloud
    version: ">=1.0.0"

---
# ansible/ansible.cfg
[defaults]
host_key_checking = False
inventory = inventory.ini
remote_user = root
private_key_file = ~/.ssh/id_rsa

[inventory]
enable_plugins = kubernetes.core.k8s

---
# ansible/inventory.ini
[local]
localhost ansible_connection=local

---
# ansible/group_vars/all.yml
# Global variables for all hosts
ansible_python_interpreter: "{{ ansible_playbook_python }}"
kubectl_path: kubectl
gcloud_path: gcloud

# Default resource limits for cost optimization
default_cpu_request: "50m"
default_memory_request: "64Mi"
default_cpu_limit: "100m" 
default_memory_limit: "128Mi"
