# terraform/main.tf
terraform {
  required_version = ">= 1.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

# Variables
variable "project_id" {
  description = "GCP Project ID"
  type        = string
  default     = "karakeep-app" 
}

variable "region" {
  description = "GCP Region"
  type        = string
  default     = "asia-southeast1"
}

variable "zone" {
  description = "GCP Zone"
  type        = string
  default     = "asia-southeast1-a"
}

# Provider
provider "google" {
  project = var.project_id
  region  = var.region
}

# Enable required APIs
resource "google_project_service" "required_apis" {
  for_each = toset([
    "container.googleapis.com",
    "compute.googleapis.com"
  ])
  
  project = var.project_id
  service = each.value
  
  disable_dependent_services = true
}

# GKE Cluster - Minimal cost configuration
resource "google_container_cluster" "karakeep_cluster" {
  name     = "karakeep-cluster"
  location = var.zone
  
  # Remove default node pool
  remove_default_node_pool = true
  initial_node_count       = 1
  
  # Minimal cluster configuration
  network    = "default"
  subnetwork = "default"
  
  # Disable features to reduce cost
  monitoring_service = "none"
  logging_service   = "none"
  
  # Network policy disabled to save cost
  network_policy {
    enabled = false
  }
  
  # Disable legacy ABAC
  enable_legacy_abac = false
  
  # Master auth
  master_auth {
    client_certificate_config {
      issue_client_certificate = false
    }
  }
  
  depends_on = [google_project_service.required_apis]
}

# Node pool - Spot instances for minimal cost
resource "google_container_node_pool" "karakeep_nodes" {
  name       = "karakeep-node-pool"
  location   = var.zone
  cluster    = google_container_cluster.karakeep_cluster.name
  node_count = 1
  
  node_config {
    # Use spot instances for 60-91% cost savings
    spot = true
    
    # Smallest machine type that can run GKE
    machine_type = "e2-micro"
    disk_size_gb = 10
    disk_type    = "pd-standard"
    
    # Use Container-Optimized OS
    image_type = "COS_CONTAINERD"
    
    # Minimal OAuth scopes
    oauth_scopes = [
      "https://www.googleapis.com/auth/devstorage.read_only",
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring",
    ]
    
    # Node metadata
    metadata = {
      disable-legacy-endpoints = "true"
    }
    
    tags = ["karakeep-node"]
  }
  
  # Autoscaling disabled to keep costs predictable
  autoscaling {
    min_node_count = 1
    max_node_count = 1
  }
  
  # Management
  management {
    auto_repair  = true
    auto_upgrade = false # Disable to avoid unexpected costs
  }
}

# Outputs
output "cluster_name" {
  value = google_container_cluster.karakeep_cluster.name
}

output "cluster_zone" {
  value = google_container_cluster.karakeep_cluster.location
}

output "cluster_endpoint" {
  value = google_container_cluster.karakeep_cluster.endpoint
}

# Get credentials command
output "get_credentials_command" {
  value = "gcloud container clusters get-credentials ${google_container_cluster.karakeep_cluster.name} --zone ${google_container_cluster.karakeep_cluster.location} --project ${var.project_id}"
}

