# Karakeep GKE Deployment

This repository contains the minimal-cost setup for deploying the Karakeep application on Google Kubernetes Engine (GKE) using Terraform and Ansible.

## 🏗️ Architecture

- **GKE Cluster**: Single-zone cluster with spot instances
- **Node Pool**: 1x e2-micro spot instance (smallest viable size)
- **Applications**: Karakeep app + Tailscale subnet router
- **LLM**: Configured for Gemini Flash 2.5 instead of OpenAI
- **Cost**: Optimized for minimum running cost (~$15-25/month)

## 📋 Prerequisites

### Required Tools
- [Terraform](https://terraform.io/downloads) >= 1.0
- [Ansible](https://docs.ansible.com/ansible/latest/installation_guide/intro_installation.html) >= 4.0
- [kubectl](https://kubernetes.io/docs/tasks/tools/install-kubectl/)
- [gcloud CLI](https://cloud.google.com/sdk/docs/install)

### GCP Setup
1. Create a GCP project (or use existing)
2. Enable billing for the project
3. Install and authenticate gcloud CLI:
   ```bash
   gcloud auth login
   gcloud config set project YOUR_PROJECT_ID
   ```

### Environment Variables
Set these environment variables before running the setup:

```bash
export GEMINI_API_KEY="your-gemini-api-key"
export TAILSCALE_AUTH_KEY="your-tailscale-auth-key"  # Optional
```

To get these keys:
- **Gemini API Key**: Get from [Google AI Studio](https://aistudio.google.com/app/apikey)
- **Tailscale Auth Key**: Get from [Tailscale Admin Console](https://login.tailscale.com/admin/settings/keys)

## 🚀 Quick Start

1. **Clone and configure**:
   ```bash
   git clone <this-repo>
   cd karakeep-gke
   
   # Update project ID in setup.sh and terraform/main.tf
   sed -i 's/jamcrank-project/YOUR_PROJECT_ID/g' setup.sh
   sed -i 's/jamcrank-project/YOUR_PROJECT_ID/g' terraform/main.tf
   ```

2. **Set environment variables**:
   ```bash
   export GEMINI_API_KEY="your-gemini-api-key"
   export TAILSCALE_AUTH_KEY="your-tailscale-auth-key"
   ```

3. **Run the setup script**:
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

The script will:
- ✅ Check prerequisites
- ✅ Deploy GKE cluster with Terraform
- ✅ Install Karakeep app with Ansible
- ✅ Install Tailscale subnet router
- ✅ Configure Gemini Flash 2.5 integration

## 📁 Project Structure

```
.
├── terraform/
│   ├── main.tf              # GKE cluster definition
│   └── terraform.tfvars     # Variable values (auto-generated)
├── ansible/
│   ├── karakeep-deploy.yml  # Main deployment playbook
│   ├── requirements.yml     # Ansible collections
│   ├── ansible.cfg          # Ansible configuration
│   └── inventory.ini        # Inventory file
├── setup.sh                 # Automated setup script
└── README.md               # This file
```

## 🔧 Manual Steps

If you prefer to run each component manually:

### 1. Deploy GKE with Terraform

```bash
cd terraform
terraform init
terraform plan
terraform apply
```

### 2. Install Ansible requirements

```bash
cd ansible
ansible-galaxy collection install -r requirements.yml
```

### 3. Configure kubectl

```bash
gcloud container clusters get-credentials karakeep-cluster \
  --zone us-central1-a \
  --project YOUR_PROJECT_ID
```

### 4. Deploy applications

```bash
ansible-playbook karakeep-deploy.yml
```

## 🌐 Accessing Karakeep

### Option 1: Port Forward (Recommended for testing)
```bash
kubectl port-forward -n karakeep svc/karakeep-service 8080:80
```
Then visit: http://localhost:8080

### Option 2: Tailscale Access
1. Approve the subnet router in your [Tailscale admin panel](https://login.tailscale.com/admin/machines)
2. Access the service via Tailscale network

## 💰 Cost Optimization Features

This setup is optimized for minimal cost:

- **Spot Instances**: 60-91% cheaper than regular instances
- **Minimal Resources**: e2-micro instances with 10GB disk
- **Single Zone**: No multi-zone redundancy
- **No Load Balancer**: Uses ClusterIP + port-forward
- **Disabled Services**: Monitoring and logging disabled
- **Resource Limits**: Strict CPU/memory limits on pods

**Estimated monthly cost**: $15-25 USD

## 📊 Monitoring and Troubleshooting

### Check deployment status
```bash
kubectl get pods -n karakeep
kubectl get services -n karakeep
```

### View application logs
```bash
kubectl logs -n karakeep deployment/karakeep
kubectl logs -n karakeep deployment/tailscale-subnet-router
```

### Check Tailscale status
```bash
kubectl exec -n karakeep deployment/tailscale-subnet-router -- tailscale status
```

### Scale application (if needed)
```bash
kubectl scale -n karakeep deployment/karakeep --replicas=2
```

## 🔄 Configuration

### Karakeep App Configuration

The app is configured with these environment variables:
- `GEMINI_API_KEY`: Your Gemini API key
- `LLM_PROVIDER`: Set to "gemini"
- `GEMINI_MODEL`: Set to "gemini-2.0-flash-exp"

### Resource Limits

Current resource allocation per pod:
- CPU Request: 50-100m
- Memory Request: 64-128Mi
- CPU Limit: 100-200m
- Memory Limit: 128-256Mi

To adjust resources, edit the Ansible playbook values.

## 🧹 Cleanup

To destroy all resources and avoid charges:

```bash
cd terraform
terraform destroy
```

This will delete:
- GKE cluster and nodes
- All deployed applications
- Associated GCP resources

## 🔒 Security Considerations

This minimal setup prioritizes cost over security. For production:

- [ ] Enable GKE monitoring and logging
- [ ] Add network policies
- [ ] Use private cluster
- [ ] Enable Workload Identity
- [ ] Add ingress controller with TLS
- [ ] Implement proper RBAC
- [ ] Use managed certificates

## ❓ Troubleshooting

### Common Issues

1. **"Insufficient quota" error**:
   - Request quota increase in GCP Console
   - Or try a different zone/region

2. **Ansible connection timeout**:
   - Ensure kubectl is configured: `kubectl cluster-info`
   - Check cluster status: `gcloud container clusters list`

3. **Spot instance preemption**:
   - Normal behavior, pod will reschedule automatically
   - For production, consider regular instances

4. **Tailscale not connecting**:
   - Check auth key is valid
   - Verify TAILSCALE_AUTH_KEY environment variable

### Getting Help

- Check logs: `kubectl logs -n karakeep <pod-name>`
- Describe resources: `kubectl describe pod -n karakeep <pod-name>`
- GKE status: `gcloud container clusters describe karakeep-cluster --zone us-central1-a`

## 📈 Scaling Up

When ready to scale beyond the minimal setup:

1. **Increase node count**:
   ```bash
   gcloud container clusters resize karakeep-cluster --num-nodes=3 --zone=us-central1-a
   ```

2. **Add monitoring**:
   - Enable GKE monitoring in Terraform
   - Add Prometheus/Grafana

3. **Add load balancer**:
   - Change service type to LoadBalancer
   - Configure ingress controller

4. **Multi-zone deployment**:
   - Update Terraform to use regional cluster
   - Add node pools in multiple zones